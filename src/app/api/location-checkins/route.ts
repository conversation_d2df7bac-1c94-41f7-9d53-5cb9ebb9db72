import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 地点打卡记录类型定义
interface LocationCheckInData {
  userId: string;
  position?: string;
  latitude: number;
  longitude: number;
}

// 查询参数类型定义（用于GET请求）
interface QueryParams {
  userId: string;
  startDate?: string;
  endDate?: string;
}

// 更新参数类型定义
interface UpdateParams {
  id: string;
  position?: string;
  latitude?: number;
  longitude?: number;
}

// 删除参数类型定义
interface DeleteParams {
  userId: string;
  id: string;
}

// 参数验证函数
function validateLatitude(lat: number): boolean {
  return lat >= -90 && lat <= 90;
}

function validateLongitude(lng: number): boolean {
  return lng >= -180 && lng <= 180;
}

function validatePosition(position?: string): boolean {
  return !position || position.length <= 100;
}

// 创建新的地点打卡记录
export async function POST(req: NextRequest) {
  try {
    const { userId, position, latitude, longitude }: LocationCheckInData =
      await req.json();

    // 参数验证
    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    if (typeof latitude !== "number" || typeof longitude !== "number") {
      return NextResponse.json(
        { error: "latitude和longitude必须是数字" },
        { status: 400 }
      );
    }

    if (!validateLatitude(latitude)) {
      return NextResponse.json(
        { error: "latitude必须在-90到90之间" },
        { status: 400 }
      );
    }

    if (!validateLongitude(longitude)) {
      return NextResponse.json(
        { error: "longitude必须在-180到180之间" },
        { status: 400 }
      );
    }

    if (!validatePosition(position)) {
      return NextResponse.json(
        { error: "position长度不能超过100字符" },
        { status: 400 }
      );
    }

    // 验证用户是否存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 使用事务创建地点打卡记录和日志记录
    const checkIn = await prisma.$transaction(async (tx) => {
      // 创建地点打卡记录
      const locationCheckIn = await tx.locationCheckIns.create({
        data: {
          userId,
          position,
          latitude,
          longitude,
        },
      });

      // 自动创建日志记录
      await tx.userLogs.create({
        data: {
          userId: userId,
          recordType: "location",
          recordId: locationCheckIn.id,
          imageList: [], // 地点打卡默认没有图片
          description: `在 ${position || "未知地点"} 进行了地点打卡`,
          isPublic: true,
          locationCheckInsId: locationCheckIn.id, // 关联到LocationCheckIns
        },
      });

      return locationCheckIn;
    });

    return NextResponse.json(
      {
        success: true,
        message: "地点打卡记录创建成功",
        data: checkIn,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("创建地点打卡记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 查询地点打卡记录
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    // 参数验证
    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证用户是否存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 构建查询条件
    const whereCondition: any = {
      userId,
    };

    // 添加时间范围过滤
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt.lte = new Date(endDate);
      }
    }

    // 查询地点打卡记录
    const checkIns = await prisma.locationCheckIns.findMany({
      where: whereCondition,
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      success: true,
      message: "查询成功",
      data: checkIns,
    });
  } catch (error) {
    console.error("查询地点打卡记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 更新地点打卡记录
export async function PATCH(req: NextRequest) {
  try {
    const { id, position, latitude, longitude }: UpdateParams =
      await req.json();

    // 参数验证
    if (!id) {
      return NextResponse.json({ error: "缺少id参数" }, { status: 400 });
    }

    // 验证经纬度（如果提供）
    if (latitude !== undefined && !validateLatitude(latitude)) {
      return NextResponse.json(
        { error: "latitude必须在-90到90之间" },
        { status: 400 }
      );
    }

    if (longitude !== undefined && !validateLongitude(longitude)) {
      return NextResponse.json(
        { error: "longitude必须在-180到180之间" },
        { status: 400 }
      );
    }

    if (!validatePosition(position)) {
      return NextResponse.json(
        { error: "position长度不能超过100字符" },
        { status: 400 }
      );
    }

    // 验证记录是否存在
    const existingCheckIn = await prisma.locationCheckIns.findUnique({
      where: { id },
    });

    if (!existingCheckIn) {
      return NextResponse.json(
        { error: "地点打卡记录不存在" },
        { status: 404 }
      );
    }

    // 构建更新数据
    const updateData: any = {};
    if (position !== undefined) updateData.position = position;
    if (latitude !== undefined) updateData.latitude = latitude;
    if (longitude !== undefined) updateData.longitude = longitude;

    // 更新记录
    const updatedCheckIn = await prisma.locationCheckIns.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json({
      success: true,
      message: "地点打卡记录更新成功",
      data: updatedCheckIn,
    });
  } catch (error) {
    console.error("更新地点打卡记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 删除地点打卡记录
export async function DELETE(req: NextRequest) {
  try {
    const { id, userId }: DeleteParams = await req.json();

    // 验证用户是否存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 参数验证
    if (!id) {
      return NextResponse.json({ error: "缺少id参数" }, { status: 400 });
    }

    // 验证记录是否存在
    const existingCheckIn = await prisma.locationCheckIns.findUnique({
      where: { id },
    });

    if (!existingCheckIn) {
      return NextResponse.json(
        { error: "地点打卡记录不存在" },
        { status: 404 }
      );
    }

    // 验证记录是否属于用户
    if (existingCheckIn.userId !== userId) {
      return NextResponse.json(
        { error: "地点打卡记录不属于该用户" },
        { status: 403 }
      );
    }

    // 删除记录
    await prisma.locationCheckIns.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: "地点打卡记录删除成功",
    });
  } catch (error) {
    console.error("删除地点打卡记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

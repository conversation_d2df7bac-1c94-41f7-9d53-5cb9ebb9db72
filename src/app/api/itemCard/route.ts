import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 创建新的卡片
export async function POST(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const {
      tags,
      description,
      title,
      imageFileName,
      imageURL,
      location,
      latitude,
      longitude,
    } = await req.json();

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 使用事务创建卡片、用户持有记录和日志记录
    const result = await prisma.$transaction(async (tx) => {
      // 创建卡片
      const itemCard = await tx.itemCard.create({
        data: {
          tags,
          description,
          title,
          imageFileName,
          imageURL,
          location,
          latitude,
          longitude,
          authorId: userId,
        },
      });

      // 创建用户持有记录（作者自动持有）
      await tx.userItemCard.create({
        data: {
          userId: userId,
          cardId: itemCard.id,
          isOwner: true,
          remark: "", // 初始备注为空
        },
      });

      // 自动创建日志记录
      await tx.userLogs.create({
        data: {
          userId: userId,
          recordType: "recognition",
          recordId: itemCard.id,
          imageList: [imageURL], // 使用卡片的图片作为日志图片
          description: `创建了新的识别卡片：${title}`,
          isPublic: true,
          itemCardId: itemCard.id, // 关联到ItemCard
        },
      });

      return itemCard;
    });

    return NextResponse.json({
      success: true,
      message: "卡片创建成功",
      data: result,
    });
  } catch (error) {
    console.error("创建卡片失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
}

// 修改卡片信息（只有作者可以修改）
export async function PATCH(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const cardId = searchParams.get("cardId");

    if (!userId || !cardId) {
      return NextResponse.json(
        { error: "缺少userId或cardId参数" },
        { status: 400 }
      );
    }

    const {
      tags,
      description,
      title,
      imageFileName,
      imageURL,
      location,
      latitude,
      longitude,
    } = await req.json();

    // 验证卡片存在且用户是作者
    const existingCard = await prisma.itemCard.findUnique({
      where: { id: cardId },
    });

    if (!existingCard) {
      return NextResponse.json({ error: "卡片不存在" }, { status: 404 });
    }

    if (existingCard.authorId !== userId) {
      return NextResponse.json(
        { error: "只有作者可以修改卡片信息" },
        { status: 403 }
      );
    }

    // 更新卡片信息
    const updatedCard = await prisma.itemCard.update({
      where: { id: cardId },
      data: {
        ...(tags !== undefined && { tags }),
        ...(description !== undefined && { description }),
        ...(title !== undefined && { title }),
        ...(imageFileName !== undefined && { imageFileName }),
        ...(imageURL !== undefined && { imageURL }),
        ...(location !== undefined && { location }),
        ...(latitude !== undefined && { latitude }),
        ...(longitude !== undefined && { longitude }),
      },
    });

    return NextResponse.json({
      success: true,
      message: "卡片信息更新成功",
      data: updatedCard,
    });
  } catch (error) {
    console.error("更新卡片失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
}
